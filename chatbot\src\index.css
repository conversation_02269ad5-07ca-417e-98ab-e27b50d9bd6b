:root {
  --accent: #8a2be2;
  --gallery-bg1: #f8fafc;
  --gallery-bg2: #e0e7ff;
  --viewer-bg1: #f8fafc;
  --viewer-bg2: #e0e7ff;
  --font-main: 'Inter', 'Segoe UI', Arial, sans-serif;
  --font-art: 'Style Script', cursive, serif;
}

@import url("https://fonts.googleapis.com/css2?family=Style+Script&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body, html, #root {
  min-height: 100vh;
  font-family: var(--font-main);
  background: linear-gradient(120deg, var(--gallery-bg1), var(--gallery-bg2));
  color: #22223b;
}

h1, h2, h3, h4, h5 {
  font-family: var(--font-art);
  color: var(--accent);
}

@media (max-width: 500px) {
  .loading-page {
    width: 100%;
    height: 100vh;
    background-image: url("/images/LoadingBackground.jpg");
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .loading-page div {
    position: relative;
    font-family: var(--font-art);
    font-size: 96px;
    color: var(--accent);
    text-shadow: 0 4px 24px rgba(138,43,226,0.18);
  }
  .loading-page div::after {
    content: " ";
    position: absolute;
    top: 0;
    right: 0;
    background-image: url("/images/robotIcon.svg");
    width: 50px;
    height: 50px;
  }
  .welcome-page{
    padding: 80px 28px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 30px;
    background: linear-gradient(120deg, var(--gallery-bg1), var(--gallery-bg2));
  }
  .welcome-page div{
    font-size: 22px;
  }
  .welcome-page .next-btn{
    color: white;
    background-image: linear-gradient(to right,#830EB1,#4A53FF);
    width: 228px;
    height: 64px;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    font-family: var(--font-main);
    font-size: 1.2rem;
    box-shadow: 0 4px 16px 0 rgba(138,43,226,0.12);
    transition: background 0.2s, box-shadow 0.2s;
  }
  .welcome-page .next-btn:hover {
    background-image: linear-gradient(to right,#4A53FF,#830EB1);
    box-shadow: 0 8px 32px 0 rgba(138,43,226,0.18);
  }
  .welcome-page a{
    text-decoration: none;
    color: white;
  }
}

@media (min-width: 501px) {
  .loading-page {
    width: 100vw;
    height: 100vh;
    background-image: url("/images/LoadingBackground.jpg");
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .loading-page div {
    position: relative;
    font-family: var(--font-art);
    font-size: 128px;
    color: var(--accent);
    text-shadow: 0 4px 24px rgba(138,43,226,0.18);
  }
  .welcome-page{
    padding: 120px 80px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 40px;
    background: linear-gradient(120deg, var(--gallery-bg1), var(--gallery-bg2));
  }
  .welcome-page div{
    font-size: 2rem;
  }
  .welcome-page .next-btn{
    color: white;
    background-image: linear-gradient(to right,#830EB1,#4A53FF);
    width: 320px;
    height: 80px;
    border-radius: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    font-family: var(--font-main);
    font-size: 1.5rem;
    box-shadow: 0 8px 32px 0 rgba(138,43,226,0.12);
    transition: background 0.2s, box-shadow 0.2s;
  }
  .welcome-page .next-btn:hover {
    background-image: linear-gradient(to right,#4A53FF,#830EB1);
    box-shadow: 0 16px 48px 0 rgba(138,43,226,0.18);
  }
  .welcome-page a{
    text-decoration: none;
    color: white;
  }
}
