// import LoadingPage from "./pages/loadingPage";
import WelcomePage from "./pages/welcomePage";
import {BrowserRouter, Routes, Route} from "react-router-dom";
import ArtworkGallery from "./components/artwork-gallery";
import ArtworkViewerPage from "./pages/artwork-viewer-page";

function App(){
  return(
    <>
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<WelcomePage/>} />
        <Route path="/gallery" element={<ArtworkGallery/>} />
        <Route path="/artwork/:id" element={<ArtworkViewerPage/>} />
      </Routes>
    </BrowserRouter>
    </>

  );
}
export default App;

