import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import ArtworkViewer from "../components/artwork-viewer";
import { artworks } from "../components/artwork-gallery";

function ArtworkViewerPage() {
    const { id } = useParams();
    const artwork = artworks.find(art => art.id === parseInt(id));
    
    if (!artwork) {
        return <div>Artwork not found</div>;
    }
    
    return (
        <ArtworkViewer artwork={artwork} />
    );
}

export default ArtworkViewerPage
