import "./artwork-gallery.css";
import { Link } from "react-router-dom";
// Artwork data
export const artworks = [
  {
    id: 1,
    src: "/images/Louvre-artwork.jpeg",
    alt: "liberty leading the people",
    about:"This is a painting by <PERSON>. It is a painting of a woman leading the people to freedom."
  },
  {
    id: 2,
    src: "/images/Frida-Kahlo-artwork.jpeg",
    alt: "Frida <PERSON>hlo Self-Portrait with <PERSON> and Hu<PERSON>bird",
    about: "This painting is by <PERSON><PERSON>. It is a self-portrait of her with a thorn necklace and a hummingbird."
  },
  {
    id: 3,
    src: "/images/Rene-Magritte-artworks.jpeg",
    alt: "Man in Bowler Hat",
    about: "This is a painting by <PERSON>. It is a painting of a man in a bowler hat."
  },
  {
    id: 4,
    src: "/images/Top 20 Rene-Magritte-artworks.jpeg",
    alt: "The lovers",
    about: "This is a painting by <PERSON>. It is a painting of two lovers."
  },
  {
    id: 5,
    src: "/images/Las dos Fridas.jpg",
    alt: " La due Fridas",
    about: "La Due Frida, or The Two Fridas, is a famous painting by Mexican artist <PERSON><PERSON>, completed in 1939 shortly after her divorce from <PERSON>. The painting portrays <PERSON><PERSON><PERSON>'s dual identity, her Mexican heritage and her European influences. The two Fridas are seated side by side, one dressed in traditional Mexican attire and the other in European clothing. The painting is a powerful representation of Kahlo's identity and her struggle to reconcile her Mexican and European identities."
  },
  {
    id: 6,
    src: "/images/<PERSON> Van Gogh's _The <PERSON>y Night__ Digital JPG file_.jpeg",
    alt: "The <PERSON>y Night",
    about: "This is a painting by <PERSON> van <PERSON>gh. It is a painting of the night sky with stars."
  },
];

export default function ArtworkGallery() {
  return (
    <div className="gallery-container">
      <h1 className="gallery-title">Select the artwork of your choice</h1>

      <div className="artwork-grid">
        {artworks.map((artwork) => (
          <div key={artwork.id} className="artwork-item" tabIndex={0} aria-label={`View artwork: ${artwork.alt}`}>
            <Link to={`/artwork/${artwork.id}`}>
              <img src={artwork.src || "/placeholder.svg"} alt={artwork.alt} />
            </Link>
          </div>
        ))}
      </div>
    </div>
  )
}

