.gallery-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--gallery-bg1, #f8fafc) 0%, var(--gallery-bg2, #e0e7ff) 100%);
  padding: 2rem 1rem;
  border-radius: 16px;
  overflow-y: auto;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.12);
}

.gallery-title {
  font-size: 2.25rem;
  font-weight: bold;
  color: var(--accent, #8a2be2);
  text-align: center;
  margin: 2rem 0 2.5rem 0;
  font-family: 'Style Script', cursive, serif;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(138,43,226,0.08);
}

.artwork-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.artwork-item {
  position: relative;
  aspect-ratio: 3 / 4;
  cursor: pointer;
  transition: transform 0.25s cubic-bezier(.4,2,.6,1), box-shadow 0.2s;
  box-shadow: 0 4px 16px 0 rgba(138,43,226,0.08);
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255,255,255,0.7);
  min-width: 64px;
  min-height: 96px;
}

.artwork-item:hover {
  transform: scale(1.04) rotate(-1deg);
  box-shadow: 0 8px 32px 0 rgba(138,43,226,0.18);
  z-index: 2;
}

.artwork-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  filter: grayscale(10%) contrast(1.08) drop-shadow(0 2px 8px rgba(0,0,0,0.08));
  transition: filter 0.3s;
}

.artwork-item:hover img {
  filter: none;
}

.artwork-item:focus {
  outline: 2px solid var(--accent, #8a2be2);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(138,43,226,0.12);
  z-index: 3;
}

.artwork-item img:focus {
  outline: 2px solid var(--accent, #8a2be2);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(138,43,226,0.12);
}

.welcome-image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0 1.5rem 0;
}

.welcome-image {
  max-width: 90vw;
  max-height: 320px;
  width: 100%;
  height: auto;
  border-radius: 24px;
  box-shadow: 0 8px 32px 0 rgba(138,43,226,0.18);
  border: 4px solid rgba(138,43,226,0.08);
  object-fit: cover;
  background: #fff;
}

.art-quote {
  display: block;
  font-family: var(--font-art);
  font-size: 1.5rem;
  color: var(--accent);
  text-align: center;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 8px rgba(138,43,226,0.08);
}

.welcome-description {
  font-size: 1.15rem;
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
  font-family: var(--font-main);
}

.soulful-welcome {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-bg-overlay {
  position: absolute;
  inset: 0;
  z-index: 0;
  background: url('/images/Vincent Van Gogh\'s _The Starry Night__ Digital JPG file_.jpeg') center/cover no-repeat;
  filter: blur(16px) brightness(0.7) saturate(1.2);
  opacity: 0.7;
  animation: fadeInBg 1.5s ease;
}

@keyframes fadeInBg {
  from { opacity: 0; }
  to { opacity: 0.7; }
}

.welcome-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 540px;
  margin: 0 auto;
  background: rgba(255,255,255,0.82);
  border-radius: 32px;
  box-shadow: 0 8px 32px 0 rgba(138,43,226,0.10);
  padding: 2.5rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInContent 1.2s cubic-bezier(.4,2,.6,1);
  gap: 1.5rem;
}

.welcome-first-paragraph {
  margin-bottom: 1.2rem;
}

.welcome-subtitle {
  margin-bottom: 1.2rem;
}

.welcome-image-wrapper {
  margin-bottom: 1.2rem;
}

.welcome-description {
  margin-bottom: 1.5rem;
}

.welcome-signature {
  margin-top: 2.5rem;
  font-family: var(--font-art);
  font-size: 0.95rem;
  color: #5a189a;
  opacity: 0.5;
  text-align: right;
  width: 100%;
  font-weight: 400;
  letter-spacing: 0.04em;
}

@keyframes fadeInContent {
  from { opacity: 0; transform: translateY(32px); }
  to { opacity: 1; transform: none; }
}

@media (max-width: 600px) {
  .welcome-content {
    padding: 1.5rem 0.5rem 1.5rem 0.5rem;
    max-width: 98vw;
  }
  .welcome-signature {
    font-size: 1rem;
  }
}

@media (min-width: 600px) {
  .artwork-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 900px) {
  .artwork-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  .gallery-title {
    font-size: 2.75rem;
  }
}

@media (min-width: 700px) {
  .welcome-image {
    max-width: 480px;
    max-height: 420px;
  }
  .art-quote {
    font-size: 2.2rem;
  }
  .welcome-description {
    font-size: 1.35rem;
  }
}
