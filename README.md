# ChatArt: Artistic Chatbot Platform

ChatArt is a modern, artistic chatbot web app that lets users explore and ask questions about famous artworks. The frontend is built with React (Vite), and the backend uses FastAPI, Langchain, and OpenAI GPT for intelligent art conversations.

---

## Features
- Responsive, gallery-inspired UI
- Ask questions about any artwork and get AI-powered insights
- FastAPI backend with Langchain & GPT
- Easy to run locally or with Docker Compose

---

## Quick Start (with Docker Compose)

1. **Set your OpenAI API key:**
   ```sh
   export OPENAI_API_KEY=sk-...your-key...
   ```

2. **Build and run everything:**
   ```sh
   docker-compose up --build
   ```

3. **Access the app:**
   - Frontend: [http://localhost:4173](http://localhost:4173)
   - Backend API: [http://localhost:8000](http://localhost:8000)

---

## Manual Setup (Local Development)

### Backend (FastAPI)
1. **Install dependencies:**
   ```sh
   cd backend
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```
2. **Set your OpenAI API key:**
   ```sh
   export OPENAI_API_KEY=sk-...your-key...
   ```
3. **Run the backend:**
   ```sh
   uvicorn main:app --reload
   ```

### Frontend (React/Vite)
1. **Install dependencies:**
   ```sh
   cd chatbot
   npm install
   ```
2. **Run the frontend:**
   ```sh
   npm run dev
   ```
3. **Visit:** [http://localhost:5173](http://localhost:5173)

---

## Project Structure
```
├── backend/         # FastAPI + Langchain backend
├── chatbot/         # React (Vite) frontend
├── docker-compose.yml
├── README.md
└── .gitignore
```

---

## Environment Variables
- `OPENAI_API_KEY` (required for backend)

---

## License
MIT 