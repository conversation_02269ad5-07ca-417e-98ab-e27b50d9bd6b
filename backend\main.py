import os
from dotenv import load_dotenv

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
from langchain_community.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

load_dotenv()

app = FastAPI()

# Allow CORS for local frontend dev
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request body model
class ChatRequest(BaseModel):
    art_name: str
    question: str

# Response model
class ChatResponse(BaseModel):
    response: str

# Set your OpenAI API key (replace with env var or config in production)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "sk-...your-key...")

@app.post("/chat", response_model=ChatResponse)
async def chat_with_art_gpt(request: ChatRequest):
    if not request.art_name:
        raise HTTPException(status_code=400, detail="art_name is required")
    try:
        llm = ChatOpenAI(openai_api_key=OPENAI_API_KEY, temperature=0.7)
        prompt = f"You are an art expert. Share an interesting insight or story about the artwork called '{request.art_name}' and answer the question: {request.question}"
        result = llm([HumanMessage(content=prompt)])
        return {"response": result.content}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
    

import uvicorn

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)