import { Link } from "react-router-dom";
import { FaHandPointRight } from "react-icons/fa";
function Welcome() {
  return (
    <section className="welcome-page soulful-welcome">
      <div className="welcome-bg-overlay" aria-hidden="true"></div>
      <div className="welcome-content">
        <div className="welcome-first-paragraph">
          <span className="art-quote">“Art is not what you see, but what you make others see”. <PERSON></span>
        </div>
        <div className="welcome-image-wrapper">
          <img className="welcome-image" src="/images/botWelcome.jpg" alt="Chatbot Welcome" />
        </div>
        <div className="welcome-description">
          Ask about any artwork to reveal its hidden stories.
        </div>
        <Link to='/gallery'>
        <div className="next-btn">
          Next <FaHandPointRight />
        </div>
        </Link>
        <div className="welcome-signature">— ChatArt</div>
      </div>
    </section>
  );
}
export default Welcome;
