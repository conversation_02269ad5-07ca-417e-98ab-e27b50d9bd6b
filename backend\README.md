# ChatArt FastAPI Backend

This is the backend for the ChatArt chatbot, built with FastAPI and Langchain (OpenAI GPT).

## Setup

1. **Install dependencies:**

```bash
pip install -r requirements.txt
```

2. **Set your OpenAI API key:**

Set the `OPENAI_API_KEY` environment variable:

```bash
export OPENAI_API_KEY=sk-...your-key...
```

3. **Run the server:**

```bash
uvicorn main:app --reload
```

## Endpoint

- `POST /chat`
  - Body: `{ "art_name": "The Starry Night" }`
  - Returns: `{ "response": "..." }` 