.artwork-viewer {
  min-height: 100vh;
  background: linear-gradient(120deg, var(--viewer-bg1, #f8fafc) 0%, var(--viewer-bg2, #e0e7ff) 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}

.header {
  padding: 32px 16px 16px 16px;
  display: flex;
  align-items: center;
}

.back-button {
  background: linear-gradient(to right, var(--accent, #9333ea), #2563eb);
  border-radius: 50px;
  padding: 16px;
  border: none;
  cursor: pointer;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s, transform 0.2s;
}

.back-button:hover {
  box-shadow: 0 16px 32px 0 rgba(138,43,226,0.18);
  transform: scale(1.08);
}

.back-button:focus {
  outline: 2px solid var(--accent, #9333ea);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(138,43,226,0.12);
}

.back-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 8vw;
  justify-content: center;
}

.artwork-container {
  width: 100%;
  max-width: 800px;
  max-height: 80vh;
  margin-bottom: 32px;
  border-radius: 28px;
  overflow: hidden;
  box-shadow: 0 12px 48px 0 rgba(31, 38, 135, 0.18);
  background: rgba(255,255,255,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.artwork-image {
  width: 100%;
  height: auto;
  max-height: 78vh;
  border-radius: 28px;
  box-shadow: 0 10px 24px -3px rgba(0, 0, 0, 0.13), 0 4px 12px -2px rgba(0, 0, 0, 0.09);
  filter: grayscale(8%) contrast(1.05);
  transition: filter 0.3s;
  object-fit: contain;
}

.artwork-image:hover {
  filter: none;
}

.description-container {
  text-align: center;
  margin-bottom: 48px;
  padding: 0 16px;
}

.description-text {
  color: #1e1e1e;
  font-size: 1.2rem;
  line-height: 1.7;
  font-family: 'Georgia', 'Times New Roman', Times, serif;
  margin: 0;
  background: rgba(255,255,255,0.7);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  box-shadow: 0 2px 8px rgba(138,43,226,0.06);
}

.chat-container {
  padding: 24px 8vw 32px 8vw;
  width: 100%;
  display: flex;
  justify-content: center;
}

.chat-input-wrapper {
  background: linear-gradient(90deg, #1e1e1e 80%, var(--accent, #9333ea) 100%);
  border-radius: 50px;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 600px;
  transition: box-shadow 0.2s;
}

.chat-input-wrapper:focus-within {
  box-shadow: 0 8px 32px 0 rgba(138,43,226,0.18);
}

.chat-input {
  background: transparent;
  color: white;
  flex: 1;
  outline: none;
  border: none;
  font-size: 1.1rem;
  font-family: inherit;
}

.chat-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(138,43,226,0.15);
  background: rgba(138,43,226,0.08);
  border: 1.5px solid rgba(138,43,226,0.18);
}

.chat-input::placeholder {
  color: #bdbdbd;
  font-style: italic;
}

.send-button {
  margin-left: 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: transform 0.2s;
}

.send-button:active {
  transform: scale(0.95);
}

.send-button:focus {
  outline: 2px solid var(--accent, #9333ea);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(138,43,226,0.12);
}

.send-icon {
  width: 22px;
  height: 22px;
  color: white;
}

.chat-history {
  width: 100%;
  max-width: 600px;
  margin: 0 auto 1.5rem auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 48px;
}

.chat-msg {
  padding: 0.75rem 1.2rem;
  border-radius: 18px;
  font-size: 1.05rem;
  max-width: 90%;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(138,43,226,0.06);
  background: #f3f0ff;
  color: #22223b;
  align-self: flex-start;
  animation: fadeInChat 0.3s;
}

.chat-msg-user {
  background: linear-gradient(90deg, #8a2be2 60%, #4A53FF 100%);
  color: #fff;
  align-self: flex-end;
  box-shadow: 0 2px 8px rgba(74,83,255,0.10);
}

.chat-msg-bot {
  background: #fffbe7;
  color: #5a189a;
  align-self: flex-start;
  border: 1px solid #e0e7ff;
}

.chat-msg-error {
  background: #ffe5e5;
  color: #b91c1c;
  border: 1px solid #fca5a5;
  align-self: center;
}

@keyframes fadeInChat {
  from { opacity: 0; transform: translateY(12px); }
  to { opacity: 1; transform: none; }
}

/* Responsive design */
@media (max-width: 900px) {
  .main-content {
    padding: 24px 4vw;
  }
  .chat-container {
    padding: 16px 4vw 24px 4vw;
  }
  .artwork-container {
    max-width: 98vw;
    max-height: 60vh;
  }
  .artwork-image {
    max-height: 58vh;
  }
}
@media (max-width: 600px) {
  .main-content {
    padding: 16px 2vw;
  }
  .chat-container {
    padding: 8px 2vw 16px 2vw;
  }
  .artwork-container {
    max-width: 98vw;
    max-height: 40vh;
  }
  .artwork-image {
    max-height: 38vh;
  }
  .description-text {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }
}
