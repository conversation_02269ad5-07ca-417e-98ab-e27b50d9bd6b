import { ArrowLeft, Send } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import "./artwork-viewer.css";

export default function ArtworkViewer({artwork}) {
  const navigate = useNavigate();
  const [question, setQuestion] = useState("");
  const [chat, setChat] = useState([]); // [{role: 'user'|'bot', text: string}]
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleBackClick = () => {
    navigate(-1); // Go back to the previous page
  };

  const handleSend = async () => {
    if (!question.trim()) return;
    setChat((prev) => [...prev, { role: "user", text: question }]);
    setLoading(true);
    setError("");
    try {
      const res = await fetch("http://localhost:8000/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ art_name: artwork.alt, question }),
      });
      if (!res.ok) throw new Error("Failed to get response");
      const data = await res.json();
      setChat((prev) => [...prev, { role: "bot", text: data.response }]);
    } catch (e) {
      setError("Sorry, something went wrong. Please try again.");
    } finally {
      setLoading(false);
      setQuestion("");
    }
  };

  const handleInputKeyDown = (e) => {
    if (e.key === "Enter") handleSend();
  };

  return (
    <div className="artwork-viewer">
      {/* Header with back button */}
      <div className="header">
        <button className="back-button" onClick={handleBackClick}>
          <ArrowLeft className="back-icon" />
        </button>
      </div>

      {/* Main content */}
      <div className="main-content">
        {/* Artwork image */}
        <div className="artwork-container">
          <img
            src={artwork.src}
            alt={artwork.alt}
            className="artwork-image"
          />
        </div>

        {/* Artwork description */}
        <div className="description-container">
          <p className="description-text">
            {artwork.about}
          </p>
        </div>
        {/* Chat history */}
        <div className="chat-history">
          {chat.map((msg, idx) => (
            <div key={idx} className={`chat-msg chat-msg-${msg.role}`}>
              {msg.text}
            </div>
          ))}
          {loading && <div className="chat-msg chat-msg-bot">Thinking...</div>}
          {error && <div className="chat-msg chat-msg-error">{error}</div>}
        </div>
      </div>

      {/* Chat input at bottom */}
      <div className="chat-container">
        <div className="chat-input-wrapper">
          <input
            type="text"
            placeholder="Ask about the artwork ..."
            className="chat-input"
            value={question}
            onChange={e => setQuestion(e.target.value)}
            onKeyDown={handleInputKeyDown}
            disabled={loading}
          />
          <button className="send-button" onClick={handleSend} disabled={loading || !question.trim()}>
            <Send className="send-icon" />
          </button>
        </div>
      </div>
    </div>
  )
}

